using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Models;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using System.Text.RegularExpressions;
using Xunit;

namespace RazeWinComTr.Tests.Unit.Services;

/// <summary>
/// Tests for email content generation, template formatting, and localization
/// </summary>
public class EmailContentTests : IDisposable
{
    private readonly AppDbContext _context;
    private readonly Mock<IVerificationService> _mockVerificationService;
    private readonly Mock<IEmailHelper> _mockEmailHelper;
    private readonly Mock<IStringLocalizer<SharedResource>> _mockLocalizer;
    private readonly Mock<ILogger<EmailVerificationService>> _mockLogger;
    private readonly Mock<IWebHostEnvironment> _mockEnvironment;
    private readonly Mock<IHttpContextAccessor> _mockHttpContextAccessor;
    private readonly EmailVerificationService _emailVerificationService;

    public EmailContentTests()
    {
        var options = new DbContextOptionsBuilder<AppDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AppDbContext(options);
        _mockVerificationService = new Mock<IVerificationService>();
        _mockEmailHelper = new Mock<IEmailHelper>();
        _mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();
        _mockLogger = new Mock<ILogger<EmailVerificationService>>();
        _mockEnvironment = new Mock<IWebHostEnvironment>();
        _mockHttpContextAccessor = new Mock<IHttpContextAccessor>();

        _emailVerificationService = new EmailVerificationService(
            _mockVerificationService.Object,
            _context,
            _mockEmailHelper.Object,
            _mockLocalizer.Object,
            _mockLogger.Object,
            _mockEnvironment.Object,
            _mockHttpContextAccessor.Object);

        SetupMocks();
        SeedTestData();
    }

    private void SetupMocks()
    {
        // Setup environment
        _mockEnvironment.Setup(e => e.EnvironmentName).Returns("Development");

        // Setup HttpContext for base URL
        var mockHttpContext = new Mock<HttpContext>();
        var mockRequest = new Mock<HttpRequest>();
        mockRequest.Setup(r => r.Scheme).Returns("https");
        mockRequest.Setup(r => r.Host).Returns(new HostString("localhost:5001"));
        mockHttpContext.Setup(c => c.Request).Returns(mockRequest.Object);
        _mockHttpContextAccessor.Setup(a => a.HttpContext).Returns(mockHttpContext.Object);

        // Setup localizer with test strings
        SetupLocalizer();

        // Setup verification service
        _mockVerificationService.Setup(v => v.CanSendVerificationAsync(It.IsAny<int>(), It.IsAny<RazeWinComTr.Areas.Admin.Enums.VerificationType>()))
            .ReturnsAsync(true);
        _mockVerificationService.Setup(v => v.GenerateVerificationTokenAsync(It.IsAny<int>(), It.IsAny<RazeWinComTr.Areas.Admin.Enums.VerificationType>(), It.IsAny<string>()))
            .ReturnsAsync("test-token-123");
    }

    private void SetupLocalizer()
    {
        _mockLocalizer.Setup(l => l["Email Verification Required"])
            .Returns(new LocalizedString("Email Verification Required", "Email Verification Required"));
        _mockLocalizer.Setup(l => l["Hello"])
            .Returns(new LocalizedString("Hello", "Hello"));
        _mockLocalizer.Setup(l => l["Thank you for registering with RazeWin. To complete your registration, please verify your email address by clicking the button below:"])
            .Returns(new LocalizedString("Thank you for registering with RazeWin. To complete your registration, please verify your email address by clicking the button below:", "Thank you for registering with RazeWin. To complete your registration, please verify your email address by clicking the button below:"));
        _mockLocalizer.Setup(l => l["Verify Email Address"])
            .Returns(new LocalizedString("Verify Email Address", "Verify Email Address"));
        _mockLocalizer.Setup(l => l["If the button doesn't work, you can copy and paste this link into your browser:"])
            .Returns(new LocalizedString("If the button doesn't work, you can copy and paste this link into your browser:", "If the button doesn't work, you can copy and paste this link into your browser:"));
        _mockLocalizer.Setup(l => l["This verification link will expire in 24 hours."])
            .Returns(new LocalizedString("This verification link will expire in 24 hours.", "This verification link will expire in 24 hours."));
        _mockLocalizer.Setup(l => l["If you didn't create an account with us, please ignore this email."])
            .Returns(new LocalizedString("If you didn't create an account with us, please ignore this email.", "If you didn't create an account with us, please ignore this email."));
        _mockLocalizer.Setup(l => l["All rights reserved."])
            .Returns(new LocalizedString("All rights reserved.", "All rights reserved."));
    }

    private void SeedTestData()
    {
        var user = new User
        {
            UserId = 1,
            Email = "<EMAIL>",
            Name = "Content",
            Surname = "Test",
            IdentityNumber = "***********",
            PhoneNumber = "+************",
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };

        _context.Users.Add(user);
        _context.SaveChanges();
    }

    [Fact]
    public async Task SendVerificationEmailAsync_ShouldGenerateCorrectEmailContent()
    {
        // Arrange
        int userId = 1;
        string email = "<EMAIL>";
        string userName = "Content Test";
        
        EmailMessage capturedMessage = null;
        _mockEmailHelper.Setup(e => e.SendEmailAsync(It.IsAny<EmailMessage>()))
            .Callback<EmailMessage>(msg => capturedMessage = msg)
            .ReturnsAsync(true);

        // Act
        await _emailVerificationService.SendVerificationEmailAsync(userId, email, userName);

        // Assert
        Assert.NotNull(capturedMessage);
        Assert.Equal("Email Verification Required", capturedMessage.Subject);
        Assert.True(capturedMessage.IsHtml);
        Assert.Single(capturedMessage.ToAddresses);
        Assert.Equal(email, capturedMessage.ToAddresses.First().Address);
    }

    [Fact]
    public async Task SendVerificationEmailAsync_EmailBody_ShouldContainRequiredElements()
    {
        // Arrange
        int userId = 1;
        string email = "<EMAIL>";
        string userName = "Content Test";
        
        EmailMessage capturedMessage = null;
        _mockEmailHelper.Setup(e => e.SendEmailAsync(It.IsAny<EmailMessage>()))
            .Callback<EmailMessage>(msg => capturedMessage = msg)
            .ReturnsAsync(true);

        // Act
        await _emailVerificationService.SendVerificationEmailAsync(userId, email, userName);

        // Assert
        Assert.NotNull(capturedMessage);
        string emailBody = capturedMessage.Body;

        // Check for user greeting
        Assert.Contains("Hello Content Test", emailBody);

        // Check for main message
        Assert.Contains("Thank you for registering with RazeWin", emailBody);

        // Check for verification button
        Assert.Contains("Verify Email Address", emailBody);

        // Check for expiry notice
        Assert.Contains("24 hours", emailBody);

        // Check for disclaimer
        Assert.Contains("If you didn't create an account", emailBody);

        // Check for copyright
        Assert.Contains("All rights reserved", emailBody);
    }

    [Fact]
    public async Task SendVerificationEmailAsync_EmailBody_ShouldContainValidVerificationUrl()
    {
        // Arrange
        int userId = 1;
        string email = "<EMAIL>";
        string userName = "Content Test";
        
        EmailMessage capturedMessage = null;
        _mockEmailHelper.Setup(e => e.SendEmailAsync(It.IsAny<EmailMessage>()))
            .Callback<EmailMessage>(msg => capturedMessage = msg)
            .ReturnsAsync(true);

        // Act
        await _emailVerificationService.SendVerificationEmailAsync(userId, email, userName);

        // Assert
        Assert.NotNull(capturedMessage);
        string emailBody = capturedMessage.Body;

        // Extract verification URL from email body
        var urlMatches = Regex.Matches(emailBody, @"https://[^""'\s]+/Verification/Verify\?token=[^""'\s&]+&type=Email");
        Assert.True(urlMatches.Count >= 1, "Email should contain at least one verification URL");

        foreach (Match match in urlMatches)
        {
            string url = match.Value;
            
            // Verify URL structure
            Assert.StartsWith("https://localhost:5001/Verification/Verify?token=", url);
            Assert.Contains("&type=Email", url);
            
            // Extract and verify token
            var tokenMatch = Regex.Match(url, @"token=([^&]+)");
            Assert.True(tokenMatch.Success, "URL should contain a token parameter");
            
            string token = tokenMatch.Groups[1].Value;
            Assert.NotEmpty(token);
            Assert.Equal("test-token-123", token);
        }
    }

    [Fact]
    public async Task SendVerificationEmailAsync_EmailBody_ShouldBeValidHtml()
    {
        // Arrange
        int userId = 1;
        string email = "<EMAIL>";
        string userName = "Content Test";
        
        EmailMessage capturedMessage = null;
        _mockEmailHelper.Setup(e => e.SendEmailAsync(It.IsAny<EmailMessage>()))
            .Callback<EmailMessage>(msg => capturedMessage = msg)
            .ReturnsAsync(true);

        // Act
        await _emailVerificationService.SendVerificationEmailAsync(userId, email, userName);

        // Assert
        Assert.NotNull(capturedMessage);
        string emailBody = capturedMessage.Body;

        // Check for basic HTML structure
        Assert.Contains("<!DOCTYPE html>", emailBody);
        Assert.Contains("<html", emailBody);
        Assert.Contains("<head>", emailBody);
        Assert.Contains("<body>", emailBody);
        Assert.Contains("</body>", emailBody);
        Assert.Contains("</html>", emailBody);

        // Check for meta tags
        Assert.Contains("<meta charset=\"UTF-8\">", emailBody);
        Assert.Contains("<meta name=\"viewport\"", emailBody);

        // Check for proper button styling
        Assert.Contains("background-color: #007bff", emailBody);
        Assert.Contains("text-decoration: none", emailBody);
    }

    [Fact]
    public async Task SendVerificationEmailAsync_WithDifferentEnvironment_ShouldUseDifferentBranding()
    {
        // Arrange
        _mockEnvironment.Setup(e => e.EnvironmentName).Returns("Production");
        
        int userId = 1;
        string email = "<EMAIL>";
        string userName = "Content Test";
        
        EmailMessage capturedMessage = null;
        _mockEmailHelper.Setup(e => e.SendEmailAsync(It.IsAny<EmailMessage>()))
            .Callback<EmailMessage>(msg => capturedMessage = msg)
            .ReturnsAsync(true);

        // Act
        await _emailVerificationService.SendVerificationEmailAsync(userId, email, userName);

        // Assert
        Assert.NotNull(capturedMessage);
        string emailBody = capturedMessage.Body;

        // In production, should use actual site name instead of generic branding
        Assert.Contains("RazeWin", emailBody);
    }

    [Theory]
    [InlineData("John Doe", "Hello John Doe")]
    [InlineData("Jane Smith", "Hello Jane Smith")]
    [InlineData("Test User", "Hello Test User")]
    [InlineData("", "Hello ")]
    public async Task SendVerificationEmailAsync_WithDifferentUserNames_ShouldPersonalizeGreeting(string userName, string expectedGreeting)
    {
        // Arrange
        int userId = 1;
        string email = "<EMAIL>";
        
        EmailMessage capturedMessage = null;
        _mockEmailHelper.Setup(e => e.SendEmailAsync(It.IsAny<EmailMessage>()))
            .Callback<EmailMessage>(msg => capturedMessage = msg)
            .ReturnsAsync(true);

        // Act
        await _emailVerificationService.SendVerificationEmailAsync(userId, email, userName);

        // Assert
        Assert.NotNull(capturedMessage);
        Assert.Contains(expectedGreeting, capturedMessage.Body);
    }

    [Fact]
    public async Task SendVerificationEmailAsync_EmailBody_ShouldIncludeCurrentYear()
    {
        // Arrange
        int userId = 1;
        string email = "<EMAIL>";
        string userName = "Content Test";
        
        EmailMessage capturedMessage = null;
        _mockEmailHelper.Setup(e => e.SendEmailAsync(It.IsAny<EmailMessage>()))
            .Callback<EmailMessage>(msg => capturedMessage = msg)
            .ReturnsAsync(true);

        // Act
        await _emailVerificationService.SendVerificationEmailAsync(userId, email, userName);

        // Assert
        Assert.NotNull(capturedMessage);
        string emailBody = capturedMessage.Body;
        
        string currentYear = DateTime.Now.Year.ToString();
        Assert.Contains($"© {currentYear}", emailBody);
    }

    [Fact]
    public async Task SendVerificationEmailAsync_EmailBody_ShouldEscapeHtmlInUserName()
    {
        // Arrange
        int userId = 1;
        string email = "<EMAIL>";
        string userName = "<script>alert('xss')</script>Malicious User";
        
        EmailMessage capturedMessage = null;
        _mockEmailHelper.Setup(e => e.SendEmailAsync(It.IsAny<EmailMessage>()))
            .Callback<EmailMessage>(msg => capturedMessage = msg)
            .ReturnsAsync(true);

        // Act
        await _emailVerificationService.SendVerificationEmailAsync(userId, email, userName);

        // Assert
        Assert.NotNull(capturedMessage);
        string emailBody = capturedMessage.Body;
        
        // Should not contain raw script tags
        Assert.DoesNotContain("<script>", emailBody);
        Assert.DoesNotContain("alert('xss')", emailBody);
        
        // Should contain escaped version or safe version
        Assert.Contains("Malicious User", emailBody);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
