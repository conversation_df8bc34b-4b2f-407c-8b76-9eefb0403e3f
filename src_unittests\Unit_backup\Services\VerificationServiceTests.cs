using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using Xunit;

namespace RazeWinComTr.Tests.Unit.Services;

public class VerificationServiceTests : IDisposable
{
    private readonly AppDbContext _context;
    private readonly IVerificationService _verificationService;
    private readonly ILogger<VerificationService> _logger;

    public VerificationServiceTests()
    {
        var options = new DbContextOptionsBuilder<AppDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AppDbContext(options);
        _logger = new LoggerFactory().CreateLogger<VerificationService>();
        _verificationService = new VerificationService(_context, _logger);

        // Seed test data
        SeedTestData();
    }

    private void SeedTestData()
    {
        var user = new User
        {
            UserId = 1,
            Email = "<EMAIL>",
            Name = "Test",
            Surname = "User",
            IdentityNumber = "12345678901",
            PhoneNumber = "+************",
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };

        _context.Users.Add(user);
        _context.SaveChanges();
    }

    [Fact]
    public async Task GenerateVerificationTokenAsync_ShouldCreateNewVerificationRecord()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;
        var targetValue = "<EMAIL>";

        // Act
        var token = await _verificationService.GenerateVerificationTokenAsync(userId, verificationType, targetValue);

        // Assert
        Assert.NotNull(token);
        Assert.NotEmpty(token);

        var verification = await _context.UserVerifications
            .FirstOrDefaultAsync(v => v.UserId == userId && v.VerificationType == verificationType);

        Assert.NotNull(verification);
        Assert.Equal(token, verification.VerificationToken);
        Assert.Equal(targetValue, verification.TargetValue);
        Assert.True(verification.TokenExpiry > DateTime.UtcNow);
        Assert.False(verification.IsVerified);
    }

    [Fact]
    public async Task VerifyTokenAsync_WithValidToken_ShouldMarkAsVerified()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;
        var targetValue = "<EMAIL>";
        var token = await _verificationService.GenerateVerificationTokenAsync(userId, verificationType, targetValue);

        // Act
        var result = await _verificationService.VerifyTokenAsync(token);

        // Assert
        Assert.True(result);

        var verification = await _context.UserVerifications
            .FirstOrDefaultAsync(v => v.UserId == userId && v.VerificationType == verificationType);

        Assert.NotNull(verification);
        Assert.True(verification.IsVerified);
        Assert.NotNull(verification.VerifiedDate);
        Assert.Null(verification.VerificationToken);
        Assert.Null(verification.TokenExpiry);
    }

    [Fact]
    public async Task VerifyTokenAsync_WithInvalidToken_ShouldReturnFalse()
    {
        // Arrange
        var invalidToken = "invalid-token";

        // Act
        var result = await _verificationService.VerifyTokenAsync(invalidToken);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task VerifyTokenAsync_WithExpiredToken_ShouldReturnFalse()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;
        var targetValue = "<EMAIL>";
        var token = await _verificationService.GenerateVerificationTokenAsync(userId, verificationType, targetValue);

        // Manually expire the token
        var verification = await _context.UserVerifications
            .FirstOrDefaultAsync(v => v.VerificationToken == token);
        verification!.TokenExpiry = DateTime.UtcNow.AddHours(-1);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.VerifyTokenAsync(token);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task CanSendVerificationAsync_NewUser_ShouldReturnTrue()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;

        // Act
        var result = await _verificationService.CanSendVerificationAsync(userId, verificationType);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task CanSendVerificationAsync_AlreadyVerified_ShouldReturnFalse()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;
        var targetValue = "<EMAIL>";
        var token = await _verificationService.GenerateVerificationTokenAsync(userId, verificationType, targetValue);
        await _verificationService.VerifyTokenAsync(token);

        // Act
        var result = await _verificationService.CanSendVerificationAsync(userId, verificationType);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task CanSendVerificationAsync_WithinRateLimit_ShouldReturnFalse()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;

        // Create a verification record with recent attempt
        var verification = new UserVerification
        {
            UserId = userId,
            VerificationType = verificationType,
            TargetValue = "<EMAIL>",
            AttemptsCount = 1,
            LastAttempt = DateTime.UtcNow.AddMinutes(-2), // 2 minutes ago (within 5-minute limit)
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };

        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.CanSendVerificationAsync(userId, verificationType);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task CanSendVerificationAsync_ExceededDailyLimit_ShouldReturnFalse()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;

        // Create a verification record with max daily attempts
        var verification = new UserVerification
        {
            UserId = userId,
            VerificationType = verificationType,
            TargetValue = "<EMAIL>",
            AttemptsCount = 5, // Max daily limit
            LastAttempt = DateTime.UtcNow.AddMinutes(-10), // Outside time limit but same day
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };

        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.CanSendVerificationAsync(userId, verificationType);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task GetTimeUntilNextVerificationAsync_WithinTimeLimit_ShouldReturnTimeSpan()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;

        var verification = new UserVerification
        {
            UserId = userId,
            VerificationType = verificationType,
            TargetValue = "<EMAIL>",
            AttemptsCount = 1,
            LastAttempt = DateTime.UtcNow.AddMinutes(-2), // 2 minutes ago
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };

        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.GetTimeUntilNextVerificationAsync(userId, verificationType);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Value.TotalMinutes > 0);
        Assert.True(result.Value.TotalMinutes <= 5);
    }

    [Fact]
    public async Task GetTodayAttemptsAsync_ShouldReturnCorrectCount()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;

        var verification = new UserVerification
        {
            UserId = userId,
            VerificationType = verificationType,
            TargetValue = "<EMAIL>",
            AttemptsCount = 3,
            LastAttempt = DateTime.UtcNow,
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };

        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.GetTodayAttemptsAsync(userId, verificationType);

        // Assert
        Assert.Equal(3, result);
    }

    [Fact]
    public async Task IsVerifiedAsync_VerifiedUser_ShouldReturnTrue()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;
        var targetValue = "<EMAIL>";
        var token = await _verificationService.GenerateVerificationTokenAsync(userId, verificationType, targetValue);
        await _verificationService.VerifyTokenAsync(token);

        // Act
        var result = await _verificationService.IsVerifiedAsync(userId, verificationType);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsVerifiedAsync_UnverifiedUser_ShouldReturnFalse()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;

        // Act
        var result = await _verificationService.IsVerifiedAsync(userId, verificationType);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task GetVerificationStatusAsync_ShouldReturnCorrectStatus()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;
        var targetValue = "<EMAIL>";
        await _verificationService.GenerateVerificationTokenAsync(userId, verificationType, targetValue);

        // Act
        var status = await _verificationService.GetVerificationStatusAsync(userId, verificationType);

        // Assert
        Assert.NotNull(status);
        Assert.False(status.IsVerified);
        Assert.Equal(targetValue, status.TargetValue);
        Assert.Equal(verificationType, status.VerificationType);
        Assert.Equal(5, status.MaxDailyAttempts);
        Assert.Equal(5, status.MinutesBetweenAttempts);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
