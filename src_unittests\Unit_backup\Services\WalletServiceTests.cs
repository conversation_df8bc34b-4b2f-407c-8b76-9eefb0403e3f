using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Tests.TestInfrastructure.Base;
using RazeWinComTr.Tests.TestInfrastructure.Factories;
using RazeWinComTr.Tests.TestInfrastructure.Utilities;
using Xunit;

namespace RazeWinComTr.Tests.Unit.Services
{
    /// <summary>
    /// Unit tests for IWalletService using interface-based testing.
    /// Demonstrates proper dependency injection and interface-based testing.
    /// </summary>
    public class WalletServiceTests : TestBase
    {
        [Fact]
        public async Task GetByUserIdAndCoinIdAsync_WithExistingWallet_ReturnsWallet()
        {
            // Arrange - Interface-based testing with mock
            var mockWalletService = CreateMockWalletService();

            var wallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 1,
                Balance = 100.50m,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };

            // Setup mock to return the wallet
            mockWalletService.Setup(x => x.GetByUserIdAndCoinIdAsync(1, 1, It.IsAny<AppDbContext>()))
                .ReturnsAsync(wallet);

            var service = mockWalletService.Object;

            // Act
            var result = await service.GetByUserIdAndCoinIdAsync(1, 1);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.UserId);
            Assert.Equal(1, result.CoinId);
            Assert.Equal(100.50m, result.Balance);

            // Verify the method was called
            mockWalletService.Verify(x => x.GetByUserIdAndCoinIdAsync(1, 1, It.IsAny<AppDbContext>()), Times.Once);
        }

        [Fact]
        public async Task GetByUserIdAndCoinIdAsync_WithNonExistentWallet_ReturnsNull()
        {
            // Arrange - Mock returns null for non-existent wallet
            var mockWalletService = CreateMockWalletService();

            mockWalletService.Setup(x => x.GetByUserIdAndCoinIdAsync(999, 999, It.IsAny<AppDbContext>()))
                .ReturnsAsync((Wallet?)null);

            var service = mockWalletService.Object;

            // Act
            var result = await service.GetByUserIdAndCoinIdAsync(999, 999);

            // Assert
            Assert.Null(result);
            mockWalletService.Verify(x => x.GetByUserIdAndCoinIdAsync(999, 999, It.IsAny<AppDbContext>()), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_WithValidWallet_CreatesWallet()
        {
            // Arrange - Mock wallet creation
            var mockWalletService = CreateMockWalletService();

            var newWallet = new Wallet
            {
                UserId = 1,
                CoinId = 2,
                Balance = 50.25m
            };

            var createdWallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 2,
                Balance = 50.25m,
                CreatedDate = DateTime.UtcNow
            };

            mockWalletService.Setup(x => x.CreateAsync(It.IsAny<Wallet>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync(createdWallet);

            var service = mockWalletService.Object;

            // Act
            var result = await service.CreateAsync(newWallet);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Id > 0); // Should have been assigned an ID
            Assert.Equal(1, result.UserId);
            Assert.Equal(2, result.CoinId);
            Assert.Equal(50.25m, result.Balance);

            // Verify the method was called
            mockWalletService.Verify(x => x.CreateAsync(It.IsAny<Wallet>(), It.IsAny<AppDbContext>()), Times.Once);
        }

        [Fact]
        public async Task GetByUserIdAsync_WithMultipleWallets_ReturnsAllUserWallets()
        {
            // Arrange - Mock multiple wallets for user
            var mockWalletService = CreateMockWalletService();

            var userWallets = new List<Wallet>
            {
                new Wallet { Id = 1, UserId = 1, CoinId = 1, Balance = 100m, CreatedDate = DateTime.UtcNow },
                new Wallet { Id = 2, UserId = 1, CoinId = 2, Balance = 200m, CreatedDate = DateTime.UtcNow }
            };

            mockWalletService.Setup(x => x.GetByUserIdAsync(1))
                .ReturnsAsync(userWallets);

            var service = mockWalletService.Object;

            // Act
            var result = await service.GetByUserIdAsync(1);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // Should only return wallets for user 1
            Assert.All(result, w => Assert.Equal(1, w.UserId));
            Assert.Contains(result, w => w.CoinId == 1 && w.Balance == 100m);
            Assert.Contains(result, w => w.CoinId == 2 && w.Balance == 200m);

            // Verify the method was called
            mockWalletService.Verify(x => x.GetByUserIdAsync(1), Times.Once);
        }

        [Fact]
        public async Task GetByUserIdAsync_WithNoWallets_ReturnsEmptyList()
        {
            // Arrange - Mock returns empty list for user with no wallets
            var mockWalletService = CreateMockWalletService();

            mockWalletService.Setup(x => x.GetByUserIdAsync(999))
                .ReturnsAsync(new List<Wallet>());

            var service = mockWalletService.Object;

            // Act
            var result = await service.GetByUserIdAsync(999);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);

            // Verify the method was called
            mockWalletService.Verify(x => x.GetByUserIdAsync(999), Times.Once);
        }
    }
}
