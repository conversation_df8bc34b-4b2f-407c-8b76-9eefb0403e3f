using Microsoft.Extensions.Localization;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Tests.TestInfrastructure.Base;

namespace RazeWinComTr.Tests.Unit.Services
{
    public class BalanceTransactionServiceImplementationTests : TestBase
    {
        private AppDbContext CreateIsolatedContext() => CreateDbContext(CreateUniqueDatabaseName("BalanceTransactionService"));

        private BalanceTransactionService CreateService(AppDbContext context)
        {
            var localizer = new Mock<IStringLocalizer<SharedResource>>();
            return new BalanceTransactionService(context, localizer.Object);
        }

        [Fact]
        public async Task CreateAsync_ShouldAddTransaction()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var user = CreateUser(1, "<EMAIL>");
            context.Users.Add(user);
            await context.SaveChangesAsync();

            var transaction = new BalanceTransaction
            {
                UserId = user.UserId,
                TransactionType = "Deposit",
                Amount = 100,
                PreviousBalance = 0,
                NewBalance = 100,
                Description = "Initial deposit",
                CreatedDate = DateTime.UtcNow
            };

            var result = await service.CreateAsync(transaction);
            Assert.NotNull(result);
            Assert.True(result.Id > 0);
            Assert.True(result.IsActive);
            Assert.Equal("Deposit", result.TransactionType);
            Assert.Single(context.BalanceTransactions);
        }

        [Fact]
        public async Task GetByIdAsync_ShouldReturnTransaction_WhenExistsAndActive()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var user = CreateUser(2, "<EMAIL>");
            context.Users.Add(user);
            var transaction = new BalanceTransaction
            {
                UserId = user.UserId,
                TransactionType = "Deposit",
                Amount = 50,
                PreviousBalance = 0,
                NewBalance = 50,
                Description = "Deposit",
                CreatedDate = DateTime.UtcNow,
                IsActive = true
            };
            context.BalanceTransactions.Add(transaction);
            await context.SaveChangesAsync();

            var found = await service.GetByIdAsync(transaction.Id);
            Assert.NotNull(found);
            Assert.Equal(transaction.Id, found!.Id);
        }

        [Fact]
        public async Task GetByIdAsync_ShouldReturnNull_WhenNotActive()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var user = CreateUser(3, "<EMAIL>");
            context.Users.Add(user);
            var transaction = new BalanceTransaction
            {
                UserId = user.UserId,
                TransactionType = "Deposit",
                Amount = 50,
                PreviousBalance = 0,
                NewBalance = 50,
                Description = "Deposit",
                CreatedDate = DateTime.UtcNow,
                IsActive = false
            };
            context.BalanceTransactions.Add(transaction);
            await context.SaveChangesAsync();

            var found = await service.GetByIdAsync(transaction.Id);
            Assert.Null(found);
        }

        [Fact]
        public async Task GetListAsync_ShouldReturnAllActiveTransactions()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var user = CreateUser(4, "<EMAIL>");
            context.Users.Add(user);
            context.BalanceTransactions.AddRange(
                new BalanceTransaction { UserId = user.UserId, TransactionType = "Deposit", Amount = 10, PreviousBalance = 0, NewBalance = 10, IsActive = true, CreatedDate = DateTime.UtcNow },
                new BalanceTransaction { UserId = user.UserId, TransactionType = "Withdrawal", Amount = 5, PreviousBalance = 10, NewBalance = 5, IsActive = true, CreatedDate = DateTime.UtcNow },
                new BalanceTransaction { UserId = user.UserId, TransactionType = "Deposit", Amount = 20, PreviousBalance = 5, NewBalance = 25, IsActive = false, CreatedDate = DateTime.UtcNow }
            );
            await context.SaveChangesAsync();

            var all = await service.GetListAsync();
            Assert.Equal(2, all.Count);
            Assert.All(all, t => Assert.True(t.IsActive));
        }

        [Fact]
        public async Task GetListAsync_FilterByUserIdAndType()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var user1 = CreateUser(5, "<EMAIL>");
            var user2 = CreateUser(6, "<EMAIL>");
            context.Users.AddRange(user1, user2);
            context.BalanceTransactions.AddRange(
                new BalanceTransaction { UserId = user1.UserId, TransactionType = "Deposit", Amount = 10, PreviousBalance = 0, NewBalance = 10, IsActive = true, CreatedDate = DateTime.UtcNow },
                new BalanceTransaction { UserId = user2.UserId, TransactionType = "Withdrawal", Amount = 5, PreviousBalance = 10, NewBalance = 5, IsActive = true, CreatedDate = DateTime.UtcNow }
            );
            await context.SaveChangesAsync();

            var filtered = await service.GetListAsync(userId: user2.UserId, transactionType: "Withdrawal");
            Assert.Single(filtered);
            Assert.Equal(user2.UserId, filtered[0].UserId);
            Assert.Equal("Withdrawal", filtered[0].TransactionType);
        }

        [Fact]
        public async Task GetByUserIdAsync_ShouldReturnOnlyActiveForUser()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var user = CreateUser(7, "<EMAIL>");
            context.Users.Add(user);
            context.BalanceTransactions.AddRange(
                new BalanceTransaction { UserId = user.UserId, TransactionType = "Deposit", Amount = 10, PreviousBalance = 0, NewBalance = 10, IsActive = true, CreatedDate = DateTime.UtcNow },
                new BalanceTransaction { UserId = user.UserId, TransactionType = "Withdrawal", Amount = 5, PreviousBalance = 10, NewBalance = 5, IsActive = false, CreatedDate = DateTime.UtcNow }
            );
            await context.SaveChangesAsync();

            var result = await service.GetByUserIdAsync(user.UserId);
            Assert.Single(result);
            Assert.Equal("Deposit", result[0].TransactionType);
        }

        [Fact]
        public async Task RecordTransactionAsync_ShouldCreateTransaction()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var user = CreateUser(8, "<EMAIL>");
            context.Users.Add(user);
            await context.SaveChangesAsync();

            var result = await service.RecordTransactionAsync(user.UserId, "Deposit", 100, 0, 100, "desc", 123, "refType");
            Assert.NotNull(result);
            Assert.Equal(user.UserId, result.UserId);
            Assert.Equal("Deposit", result.TransactionType);
            Assert.Equal(100, result.Amount);
            Assert.Equal(0, result.PreviousBalance);
            Assert.Equal(100, result.NewBalance);
            Assert.Equal("desc", result.Description);
            Assert.Equal(123, result.ReferenceId);
            Assert.Equal("refType", result.ReferenceType);
            Assert.True(result.IsActive);
        }

        [Fact]
        public async Task RecordTransactionAsync_WithExistingContext_ShouldWork()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var user = CreateUser(9, "<EMAIL>");
            context.Users.Add(user);
            await context.SaveChangesAsync();

            var result = await service.RecordTransactionAsync(user.UserId, "Deposit", 100, 0, 100, existingContext: context);
            Assert.NotNull(result);
            Assert.Equal(user.UserId, result.UserId);
        }

        [Fact]
        public async Task DeleteAsync_ShouldSoftDelete()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var user = CreateUser(10, "<EMAIL>");
            context.Users.Add(user);
            var transaction = new BalanceTransaction
            {
                UserId = user.UserId,
                TransactionType = "Deposit",
                Amount = 10,
                PreviousBalance = 0,
                NewBalance = 10,
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            };
            context.BalanceTransactions.Add(transaction);
            await context.SaveChangesAsync();

            await service.DeleteAsync(transaction.Id);
            var updated = await context.BalanceTransactions.FindAsync(transaction.Id);
            Assert.NotNull(updated);
            Assert.False(updated!.IsActive);
        }

        [Fact]
        public async Task DeleteAsync_ShouldDoNothing_IfNotFound()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            // No transaction in DB
            await service.DeleteAsync(9999); // Should not throw
        }

        [Fact]
        public async Task UpdateAsync_ShouldModifyTransaction()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var user = CreateUser(11, "<EMAIL>");
            context.Users.Add(user);
            var transaction = new BalanceTransaction
            {
                UserId = user.UserId,
                TransactionType = "Deposit",
                Amount = 10,
                PreviousBalance = 0,
                NewBalance = 10,
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            };
            context.BalanceTransactions.Add(transaction);
            await context.SaveChangesAsync();

            transaction.Amount = 99;
            await service.UpdateAsync(transaction);
            var updated = await context.BalanceTransactions.FindAsync(transaction.Id);
            Assert.Equal(99, updated!.Amount);
        }
    }
}
