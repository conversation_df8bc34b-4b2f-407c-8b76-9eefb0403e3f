using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Models;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using System.Text.RegularExpressions;
using Xunit;

namespace RazeWinComTr.Tests.Integration;

/// <summary>
/// Simple integration tests for email verification flow
/// </summary>
public class SimpleEmailVerificationIntegrationTests : IDisposable
{
    private readonly AppDbContext _context;
    private readonly IVerificationService _verificationService;
    private readonly Mock<IEmailHelper> _mockEmailHelper;

    public SimpleEmailVerificationIntegrationTests()
    {
        var options = new DbContextOptionsBuilder<AppDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AppDbContext(options);
        
        // Setup mock email helper
        _mockEmailHelper = new Mock<IEmailHelper>();
        
        // Create verification service
        var logger = new LoggerFactory().CreateLogger<VerificationService>();
        _verificationService = new VerificationService(_context, logger);
    }

    [Fact]
    public async Task VerificationService_GenerateAndVerifyToken_ShouldWork()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;
        var targetValue = "<EMAIL>";

        // Act 1: Generate token
        var token = await _verificationService.GenerateVerificationTokenAsync(userId, verificationType, targetValue);

        // Assert 1: Token should be generated
        Assert.NotNull(token);
        Assert.NotEmpty(token);

        // Act 2: Verify token
        var verificationResult = await _verificationService.VerifyTokenAsync(token);

        // Assert 2: Token verification should succeed
        Assert.True(verificationResult);

        // Act 3: Check verification status
        var isVerified = await _verificationService.IsVerifiedAsync(userId, verificationType);

        // Assert 3: User should be verified
        Assert.True(isVerified);
    }

    [Fact]
    public async Task VerificationService_InvalidToken_ShouldFail()
    {
        // Arrange
        var invalidToken = "invalid-token-123";

        // Act
        var verificationResult = await _verificationService.VerifyTokenAsync(invalidToken);

        // Assert
        Assert.False(verificationResult);
    }

    [Fact]
    public async Task VerificationService_ExpiredToken_ShouldFail()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;
        var targetValue = "<EMAIL>";

        // Generate token
        var token = await _verificationService.GenerateVerificationTokenAsync(userId, verificationType, targetValue);

        // Manually expire the token
        var verification = await _context.UserVerifications
            .FirstOrDefaultAsync(v => v.VerificationToken == token);
        verification!.TokenExpiry = DateTime.UtcNow.AddHours(-1);
        await _context.SaveChangesAsync();

        // Act
        var verificationResult = await _verificationService.VerifyTokenAsync(token);

        // Assert
        Assert.False(verificationResult);
    }

    [Fact]
    public async Task VerificationService_RateLimiting_ShouldWork()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;

        // Act 1: First attempt should be allowed
        var canSendFirst = await _verificationService.CanSendVerificationAsync(userId, verificationType);

        // Assert 1
        Assert.True(canSendFirst);

        // Act 2: Generate token (this counts as an attempt)
        await _verificationService.GenerateVerificationTokenAsync(userId, verificationType, "<EMAIL>");

        // Act 3: Second attempt immediately should be rate limited
        var canSendSecond = await _verificationService.CanSendVerificationAsync(userId, verificationType);

        // Assert 3
        Assert.False(canSendSecond);

        // Act 4: Check time until next attempt
        var timeUntilNext = await _verificationService.GetTimeUntilNextVerificationAsync(userId, verificationType);

        // Assert 4
        Assert.NotNull(timeUntilNext);
        Assert.True(timeUntilNext.Value.TotalMinutes > 0);
        Assert.True(timeUntilNext.Value.TotalMinutes <= 5);
    }

    [Fact]
    public async Task VerificationService_AlreadyVerified_ShouldNotAllowMoreAttempts()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;
        var targetValue = "<EMAIL>";

        // Generate and verify token
        var token = await _verificationService.GenerateVerificationTokenAsync(userId, verificationType, targetValue);
        await _verificationService.VerifyTokenAsync(token);

        // Act
        var canSend = await _verificationService.CanSendVerificationAsync(userId, verificationType);

        // Assert
        Assert.False(canSend);
    }

    [Fact]
    public async Task VerificationService_GetVerificationStatus_ShouldReturnCorrectInfo()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;
        var targetValue = "<EMAIL>";

        // Generate token
        await _verificationService.GenerateVerificationTokenAsync(userId, verificationType, targetValue);

        // Act
        var status = await _verificationService.GetVerificationStatusAsync(userId, verificationType);

        // Assert
        Assert.NotNull(status);
        Assert.False(status.IsVerified);
        Assert.Equal(targetValue, status.TargetValue);
        Assert.Equal(verificationType, status.VerificationType);
        Assert.Equal(5, status.MaxDailyAttempts);
        Assert.Equal(5, status.MinutesBetweenAttempts);
        Assert.True(status.TodayAttempts >= 1);
    }

    [Fact]
    public async Task VerificationService_MultipleUsers_ShouldBeIndependent()
    {
        // Arrange
        int userId1 = 1;
        int userId2 = 2;
        var verificationType = VerificationType.Email;

        // Act: Generate tokens for both users
        var token1 = await _verificationService.GenerateVerificationTokenAsync(userId1, verificationType, "<EMAIL>");
        var token2 = await _verificationService.GenerateVerificationTokenAsync(userId2, verificationType, "<EMAIL>");

        // Verify only first user
        await _verificationService.VerifyTokenAsync(token1);

        // Assert: First user should be verified, second should not
        var isUser1Verified = await _verificationService.IsVerifiedAsync(userId1, verificationType);
        var isUser2Verified = await _verificationService.IsVerifiedAsync(userId2, verificationType);

        Assert.True(isUser1Verified);
        Assert.False(isUser2Verified);

        // First user should not be able to send more emails
        var canUser1Send = await _verificationService.CanSendVerificationAsync(userId1, verificationType);
        Assert.False(canUser1Send);

        // Second user should still be able to send emails (not rate limited by first user)
        var canUser2Send = await _verificationService.CanSendVerificationAsync(userId2, verificationType);
        Assert.False(canUser2Send); // False because they just generated a token
    }

    [Fact]
    public async Task VerificationService_IsTokenAlreadyVerified_ShouldWork()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;
        var targetValue = "<EMAIL>";

        var token = await _verificationService.GenerateVerificationTokenAsync(userId, verificationType, targetValue);

        // Act 1: Check before verification
        var isAlreadyVerifiedBefore = await _verificationService.IsTokenAlreadyVerifiedAsync(token);

        // Assert 1
        Assert.False(isAlreadyVerifiedBefore);

        // Act 2: Verify token
        await _verificationService.VerifyTokenAsync(token);

        // Act 3: Check after verification
        var isAlreadyVerifiedAfter = await _verificationService.IsTokenAlreadyVerifiedAsync(token);

        // Assert 3
        Assert.True(isAlreadyVerifiedAfter);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
