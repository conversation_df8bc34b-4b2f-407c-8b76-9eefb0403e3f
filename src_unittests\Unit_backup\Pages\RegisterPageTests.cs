using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Pages;
using System.ComponentModel.DataAnnotations;
using Xunit;

namespace RazeWinComTr.Tests.Unit.Pages;

/// <summary>
/// Tests for user registration flow including email verification sending
/// </summary>
public class RegisterPageTests : IDisposable
{
    private readonly AppDbContext _context;
    private readonly Mock<IEmailVerificationService> _mockEmailVerificationService;
    private readonly Mock<IHttpContextHelper> _mockHttpContextHelper;
    private readonly Mock<IStringLocalizer<SharedResource>> _mockLocalizer;
    private readonly Mock<ILogger<RegisterModel>> _mockLogger;
    private readonly RegisterModel _registerModel;

    public RegisterPageTests()
    {
        var options = new DbContextOptionsBuilder<AppDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AppDbContext(options);
        _mockEmailVerificationService = new Mock<IEmailVerificationService>();
        _mockHttpContextHelper = new Mock<IHttpContextHelper>();
        _mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();
        _mockLogger = new Mock<ILogger<RegisterModel>>();

        _registerModel = new RegisterModel(
            _context,
            _mockEmailVerificationService.Object,
            _mockHttpContextHelper.Object,
            _mockLocalizer.Object,
            _mockLogger.Object);

        SetupMocks();
    }

    private void SetupMocks()
    {
        // Setup localizer
        _mockLocalizer.Setup(l => l["Registration successful"])
            .Returns(new LocalizedString("Registration successful", "Registration successful"));
        _mockLocalizer.Setup(l => l["Email address is already registered"])
            .Returns(new LocalizedString("Email address is already registered", "Email address is already registered"));
        _mockLocalizer.Setup(l => l["Identity number is already registered"])
            .Returns(new LocalizedString("Identity number is already registered", "Identity number is already registered"));

        // Setup HttpContext helper
        _mockHttpContextHelper.Setup(h => h.StartUserSession(
            It.IsAny<User>(), It.IsAny<string>(), It.IsAny<string>()))
            .Returns(Task.CompletedTask);
    }

    [Fact]
    public async Task OnPostAsync_WithValidData_ShouldRegisterUserAndSendVerificationEmail()
    {
        // Arrange
        _registerModel.Input = new RegisterModel.InputModel
        {
            Email = "<EMAIL>",
            Name = "Register",
            Surname = "Test",
            IdentityNumber = "12345678901",
            PhoneNumber = "+905551234567",
            Password = "TestPassword123!",
            ConfirmPassword = "TestPassword123!",
            BirthDate = new DateTime(1990, 1, 1),
            AcceptTerms = true
        };

        _mockEmailVerificationService.Setup(e => e.SendVerificationEmailAsync(
            It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(true);

        // Act
        var result = await _registerModel.OnPostAsync();

        // Assert
        var redirectResult = Assert.IsType<RedirectToPageResult>(result);
        Assert.Equal("/", redirectResult.PageName);

        // Verify user was created
        var user = await _context.Users.FirstOrDefaultAsync(u => u.Email == "<EMAIL>");
        Assert.NotNull(user);
        Assert.Equal("Register", user.Name);
        Assert.Equal("Test", user.Surname);
        Assert.Equal("12345678901", user.IdentityNumber);

        // Verify email verification was attempted
        _mockEmailVerificationService.Verify(e => e.SendVerificationEmailAsync(
            user.UserId, "<EMAIL>", "Register Test"), Times.Once);

        // Verify user session was started
        _mockHttpContextHelper.Verify(h => h.StartUserSession(
            It.Is<User>(u => u.Email == "<EMAIL>"), "/", It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task OnPostAsync_WithEmailVerificationFailure_ShouldStillCompleteRegistration()
    {
        // Arrange
        _registerModel.Input = new RegisterModel.InputModel
        {
            Email = "<EMAIL>",
            Name = "Register2",
            Surname = "Test2",
            IdentityNumber = "12345678902",
            PhoneNumber = "+905551234568",
            Password = "TestPassword123!",
            ConfirmPassword = "TestPassword123!",
            BirthDate = new DateTime(1990, 1, 1),
            AcceptTerms = true
        };

        // Email verification fails
        _mockEmailVerificationService.Setup(e => e.SendVerificationEmailAsync(
            It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);

        // Act
        var result = await _registerModel.OnPostAsync();

        // Assert
        var redirectResult = Assert.IsType<RedirectToPageResult>(result);
        Assert.Equal("/", redirectResult.PageName);

        // Verify user was still created despite email failure
        var user = await _context.Users.FirstOrDefaultAsync(u => u.Email == "<EMAIL>");
        Assert.NotNull(user);

        // Verify email verification was attempted
        _mockEmailVerificationService.Verify(e => e.SendVerificationEmailAsync(
            It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()), Times.Once);

        // Verify user session was still started
        _mockHttpContextHelper.Verify(h => h.StartUserSession(
            It.IsAny<User>(), "/", It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task OnPostAsync_WithEmailVerificationException_ShouldStillCompleteRegistration()
    {
        // Arrange
        _registerModel.Input = new RegisterModel.InputModel
        {
            Email = "<EMAIL>",
            Name = "Register3",
            Surname = "Test3",
            IdentityNumber = "12345678903",
            PhoneNumber = "+905551234569",
            Password = "TestPassword123!",
            ConfirmPassword = "TestPassword123!",
            BirthDate = new DateTime(1990, 1, 1),
            AcceptTerms = true
        };

        // Email verification throws exception
        _mockEmailVerificationService.Setup(e => e.SendVerificationEmailAsync(
            It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ThrowsAsync(new Exception("Email service unavailable"));

        // Act
        var result = await _registerModel.OnPostAsync();

        // Assert
        var redirectResult = Assert.IsType<RedirectToPageResult>(result);
        Assert.Equal("/", redirectResult.PageName);

        // Verify user was still created despite email exception
        var user = await _context.Users.FirstOrDefaultAsync(u => u.Email == "<EMAIL>");
        Assert.NotNull(user);

        // Verify user session was still started
        _mockHttpContextHelper.Verify(h => h.StartUserSession(
            It.IsAny<User>(), "/", It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task OnPostAsync_WithDuplicateEmail_ShouldReturnError()
    {
        // Arrange - Create existing user
        var existingUser = new User
        {
            Email = "<EMAIL>",
            Name = "Existing",
            Surname = "User",
            IdentityNumber = "11111111111",
            PhoneNumber = "+905551111111",
            Password = "hashedpassword",
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };
        _context.Users.Add(existingUser);
        await _context.SaveChangesAsync();

        _registerModel.Input = new RegisterModel.InputModel
        {
            Email = "<EMAIL>", // Same email
            Name = "New",
            Surname = "User",
            IdentityNumber = "12345678904",
            PhoneNumber = "+905551234570",
            Password = "TestPassword123!",
            ConfirmPassword = "TestPassword123!",
            BirthDate = new DateTime(1990, 1, 1),
            AcceptTerms = true
        };

        // Act
        var result = await _registerModel.OnPostAsync();

        // Assert
        var pageResult = Assert.IsType<PageResult>(result);
        Assert.False(_registerModel.ModelState.IsValid);
        Assert.True(_registerModel.ModelState.ContainsKey("Input.Email"));

        // Verify no email verification was attempted
        _mockEmailVerificationService.Verify(e => e.SendVerificationEmailAsync(
            It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);

        // Verify no user session was started
        _mockHttpContextHelper.Verify(h => h.StartUserSession(
            It.IsAny<User>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task OnPostAsync_WithDuplicateIdentityNumber_ShouldReturnError()
    {
        // Arrange - Create existing user
        var existingUser = new User
        {
            Email = "<EMAIL>",
            Name = "Existing",
            Surname = "User",
            IdentityNumber = "12345678901",
            PhoneNumber = "+905551111111",
            Password = "hashedpassword",
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };
        _context.Users.Add(existingUser);
        await _context.SaveChangesAsync();

        _registerModel.Input = new RegisterModel.InputModel
        {
            Email = "<EMAIL>",
            Name = "New",
            Surname = "User",
            IdentityNumber = "12345678901", // Same identity number
            PhoneNumber = "+905551234570",
            Password = "TestPassword123!",
            ConfirmPassword = "TestPassword123!",
            BirthDate = new DateTime(1990, 1, 1),
            AcceptTerms = true
        };

        // Act
        var result = await _registerModel.OnPostAsync();

        // Assert
        var pageResult = Assert.IsType<PageResult>(result);
        Assert.False(_registerModel.ModelState.IsValid);
        Assert.True(_registerModel.ModelState.ContainsKey("Input.IdentityNumber"));

        // Verify no email verification was attempted
        _mockEmailVerificationService.Verify(e => e.SendVerificationEmailAsync(
            It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }

    [Theory]
    [InlineData("", "Name is required")]
    [InlineData("invalid-email", "Invalid email format")]
    [InlineData("test@", "Invalid email format")]
    public async Task OnPostAsync_WithInvalidInput_ShouldReturnValidationErrors(string email, string expectedErrorType)
    {
        // Arrange
        _registerModel.Input = new RegisterModel.InputModel
        {
            Email = email,
            Name = "",
            Surname = "Test",
            IdentityNumber = "12345678901",
            PhoneNumber = "+905551234567",
            Password = "TestPassword123!",
            ConfirmPassword = "TestPassword123!",
            BirthDate = new DateTime(1990, 1, 1),
            AcceptTerms = true
        };

        // Manually validate the model to simulate model binding validation
        var validationContext = new ValidationContext(_registerModel.Input);
        var validationResults = new List<ValidationResult>();
        Validator.TryValidateObject(_registerModel.Input, validationContext, validationResults, true);

        foreach (var validationResult in validationResults)
        {
            foreach (var memberName in validationResult.MemberNames)
            {
                _registerModel.ModelState.AddModelError($"Input.{memberName}", validationResult.ErrorMessage);
            }
        }

        // Act
        var result = await _registerModel.OnPostAsync();

        // Assert
        var pageResult = Assert.IsType<PageResult>(result);
        Assert.False(_registerModel.ModelState.IsValid);

        // Verify no email verification was attempted
        _mockEmailVerificationService.Verify(e => e.SendVerificationEmailAsync(
            It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
