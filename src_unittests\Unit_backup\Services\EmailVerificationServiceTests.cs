using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Models;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.Tests.Unit.Services;

public class EmailVerificationServiceTests : IDisposable
{
    private readonly AppDbContext _context;
    private readonly Mock<IVerificationService> _mockVerificationService;
    private readonly Mock<IEmailHelper> _mockEmailHelper;
    private readonly Mock<IStringLocalizer<SharedResource>> _mockLocalizer;
    private readonly Mock<ILogger<EmailVerificationService>> _mockLogger;
    private readonly Mock<IWebHostEnvironment> _mockEnvironment;
    private readonly Mock<IHttpContextAccessor> _mockHttpContextAccessor;
    private readonly EmailVerificationService _emailVerificationService;

    public EmailVerificationServiceTests()
    {
        var options = new DbContextOptionsBuilder<AppDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AppDbContext(options);
        _mockVerificationService = new Mock<IVerificationService>(MockBehavior.Strict);
        _mockEmailHelper = new Mock<IEmailHelper>(MockBehavior.Strict);
        _mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();
        _mockLogger = new Mock<ILogger<EmailVerificationService>>();
        _mockEnvironment = new Mock<IWebHostEnvironment>(MockBehavior.Strict);
        _mockHttpContextAccessor = new Mock<IHttpContextAccessor>(MockBehavior.Strict);

        _emailVerificationService = new EmailVerificationService(
            _mockVerificationService.Object,
            _context,
            _mockEmailHelper.Object,
            _mockLocalizer.Object,
            _mockLogger.Object,
            _mockEnvironment.Object,
            _mockHttpContextAccessor.Object);

        // Setup default mock returns
        SetupMockDefaults();
        SeedTestData();
    }

    private void SetupMockDefaults()
    {
        // Setup environment as Development for tests
        _mockEnvironment.Setup(e => e.EnvironmentName).Returns("Development");

        // Setup HttpContext for GetBaseUrl method
        var mockHttpContext = new Mock<HttpContext>();
        var mockRequest = new Mock<HttpRequest>();
        mockRequest.Setup(r => r.Scheme).Returns("https");
        mockRequest.Setup(r => r.Host).Returns(new HostString("localhost:5001"));
        mockHttpContext.Setup(c => c.Request).Returns(mockRequest.Object);
        _mockHttpContextAccessor.Setup(a => a.HttpContext).Returns(mockHttpContext.Object);

        _mockLocalizer.Setup(l => l["Email Verification Required"])
            .Returns(new LocalizedString("Email Verification Required", "Email Verification Required"));
        _mockLocalizer.Setup(l => l["Hello"])
            .Returns(new LocalizedString("Hello", "Hello"));
        _mockLocalizer.Setup(l => l["Thank you for registering with RazeWin. To complete your registration, please verify your email address by clicking the button below:"])
            .Returns(new LocalizedString("Thank you for registering with RazeWin. To complete your registration, please verify your email address by clicking the button below:", "Thank you for registering with RazeWin. To complete your registration, please verify your email address by clicking the button below:"));
        _mockLocalizer.Setup(l => l["Verify Email Address"])
            .Returns(new LocalizedString("Verify Email Address", "Verify Email Address"));
        _mockLocalizer.Setup(l => l["If the button doesn't work, you can copy and paste this link into your browser:"])
            .Returns(new LocalizedString("If the button doesn't work, you can copy and paste this link into your browser:", "If the button doesn't work, you can copy and paste this link into your browser:"));
        _mockLocalizer.Setup(l => l["This verification link will expire in 24 hours."])
            .Returns(new LocalizedString("This verification link will expire in 24 hours.", "This verification link will expire in 24 hours."));
        _mockLocalizer.Setup(l => l["If you didn't create an account with us, please ignore this email."])
            .Returns(new LocalizedString("If you didn't create an account with us, please ignore this email.", "If you didn't create an account with us, please ignore this email."));
        _mockLocalizer.Setup(l => l["All rights reserved."])
            .Returns(new LocalizedString("All rights reserved.", "All rights reserved."));
    }

    private void SeedTestData()
    {
        var user = new User
        {
            UserId = 1,
            Email = "<EMAIL>",
            Name = "Test",
            Surname = "User",
            IdentityNumber = "***********",
            PhoneNumber = "+************",
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };

        _context.Users.Add(user);
        _context.SaveChanges();
    }

    [Fact]
    public async Task GenerateVerificationTokenAsync_ShouldCallVerificationService()
    {
        // Arrange
        int userId = 1;
        var expectedToken = "test-token";
        _mockVerificationService.Setup(v => v.GenerateVerificationTokenAsync(userId, VerificationType.Email, "<EMAIL>"))
            .ReturnsAsync(expectedToken);

        // Act
        var result = await _emailVerificationService.GenerateVerificationTokenAsync(userId);

        // Assert
        Assert.Equal(expectedToken, result);
        _mockVerificationService.Verify(v => v.GenerateVerificationTokenAsync(userId, VerificationType.Email, "<EMAIL>"), Times.Once);
    }

    [Fact]
    public async Task GenerateVerificationTokenAsync_UserNotFound_ShouldThrowException()
    {
        // Arrange
        int nonExistentUserId = 999;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() =>
            _emailVerificationService.GenerateVerificationTokenAsync(nonExistentUserId));
    }

    [Fact]
    public async Task SendVerificationEmailAsync_WithValidData_ShouldSendEmail()
    {
        // Arrange
        int userId = 1;
        string email = "<EMAIL>";
        string userName = "Test User";
        string token = "test-token";

        _mockVerificationService.Setup(v => v.CanSendVerificationAsync(userId, VerificationType.Email))
            .ReturnsAsync(true);
        _mockVerificationService.Setup(v => v.GenerateVerificationTokenAsync(userId, VerificationType.Email, email))
            .ReturnsAsync(token);
        _mockEmailHelper.Setup(e => e.SendEmailAsync(It.IsAny<EmailMessage>()))
            .ReturnsAsync(true);

        // Act
        var result = await _emailVerificationService.SendVerificationEmailAsync(userId, email, userName);

        // Assert
        Assert.True(result);
        _mockEmailHelper.Verify(e => e.SendEmailAsync(It.Is<EmailMessage>(msg =>
            msg.ToAddresses.Any(addr => addr.Address == email) &&
            msg.Subject == "Email Verification Required" &&
            msg.IsHtml == true)), Times.Once);
    }

    [Fact]
    public async Task SendVerificationEmailAsync_RateLimited_ShouldReturnFalse()
    {
        // Arrange
        int userId = 1;
        string email = "<EMAIL>";
        string userName = "Test User";

        _mockVerificationService.Setup(v => v.CanSendVerificationAsync(userId, VerificationType.Email))
            .ReturnsAsync(false);

        // Act
        var result = await _emailVerificationService.SendVerificationEmailAsync(userId, email, userName);

        // Assert
        Assert.False(result);
        _mockEmailHelper.Verify(e => e.SendEmailAsync(It.IsAny<EmailMessage>()), Times.Never);
    }

    [Fact]
    public async Task SendVerificationEmailAsync_EmailSendFails_ShouldReturnFalse()
    {
        // Arrange
        int userId = 1;
        string email = "<EMAIL>";
        string userName = "Test User";
        string token = "test-token";

        _mockVerificationService.Setup(v => v.CanSendVerificationAsync(userId, VerificationType.Email))
            .ReturnsAsync(true);
        _mockVerificationService.Setup(v => v.GenerateVerificationTokenAsync(userId, VerificationType.Email, email))
            .ReturnsAsync(token);
        _mockEmailHelper.Setup(e => e.SendEmailAsync(It.IsAny<EmailMessage>()))
            .ReturnsAsync(false);

        // Act
        var result = await _emailVerificationService.SendVerificationEmailAsync(userId, email, userName);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task VerifyEmailTokenAsync_ShouldCallVerificationService()
    {
        // Arrange
        string token = "test-token";
        _mockVerificationService.Setup(v => v.VerifyTokenAsync(token))
            .ReturnsAsync(true);

        // Act
        var result = await _emailVerificationService.VerifyEmailTokenAsync(token);

        // Assert
        Assert.True(result);
        _mockVerificationService.Verify(v => v.VerifyTokenAsync(token), Times.Once);
    }

    [Fact]
    public async Task CanSendVerificationEmailAsync_ShouldCallVerificationService()
    {
        // Arrange
        int userId = 1;
        _mockVerificationService.Setup(v => v.CanSendVerificationAsync(userId, VerificationType.Email))
            .ReturnsAsync(true);

        // Act
        var result = await _emailVerificationService.CanSendVerificationEmailAsync(userId);

        // Assert
        Assert.True(result);
        _mockVerificationService.Verify(v => v.CanSendVerificationAsync(userId, VerificationType.Email), Times.Once);
    }

    [Fact]
    public async Task GetTimeUntilNextEmailAsync_ShouldCallVerificationService()
    {
        // Arrange
        int userId = 1;
        var expectedTimeSpan = TimeSpan.FromMinutes(3);
        _mockVerificationService.Setup(v => v.GetTimeUntilNextVerificationAsync(userId, VerificationType.Email))
            .ReturnsAsync(expectedTimeSpan);

        // Act
        var result = await _emailVerificationService.GetTimeUntilNextEmailAsync(userId);

        // Assert
        Assert.Equal(expectedTimeSpan, result);
        _mockVerificationService.Verify(v => v.GetTimeUntilNextVerificationAsync(userId, VerificationType.Email), Times.Once);
    }

    [Fact]
    public async Task GetTodayAttemptsAsync_ShouldCallVerificationService()
    {
        // Arrange
        int userId = 1;
        int expectedAttempts = 2;
        _mockVerificationService.Setup(v => v.GetTodayAttemptsAsync(userId, VerificationType.Email))
            .ReturnsAsync(expectedAttempts);

        // Act
        var result = await _emailVerificationService.GetTodayAttemptsAsync(userId);

        // Assert
        Assert.Equal(expectedAttempts, result);
        _mockVerificationService.Verify(v => v.GetTodayAttemptsAsync(userId, VerificationType.Email), Times.Once);
    }

    [Fact]
    public async Task IsEmailVerifiedAsync_ShouldCallVerificationService()
    {
        // Arrange
        int userId = 1;
        _mockVerificationService.Setup(v => v.IsVerifiedAsync(userId, VerificationType.Email))
            .ReturnsAsync(true);

        // Act
        var result = await _emailVerificationService.IsEmailVerifiedAsync(userId);

        // Assert
        Assert.True(result);
        _mockVerificationService.Verify(v => v.IsVerifiedAsync(userId, VerificationType.Email), Times.Once);
    }

    [Fact]
    public async Task GetVerificationStatusAsync_ShouldReturnEmailVerificationStatus()
    {
        // Arrange
        int userId = 1;
        var verificationStatus = new VerificationStatus
        {
            IsVerified = true,
            VerifiedDate = DateTime.UtcNow,
            CanSendVerification = false,
            TimeUntilNextVerification = null,
            TodayAttempts = 1,
            MaxDailyAttempts = 5,
            MinutesBetweenAttempts = 5,
            TargetValue = "<EMAIL>",
            VerificationType = VerificationType.Email
        };

        _mockVerificationService.Setup(v => v.GetVerificationStatusAsync(userId, VerificationType.Email))
            .ReturnsAsync(verificationStatus);

        // Act
        var result = await _emailVerificationService.GetVerificationStatusAsync(userId);

        // Assert
        Assert.IsType<EmailVerificationStatus>(result);
        Assert.Equal(verificationStatus.IsVerified, result.IsVerified);
        Assert.Equal(verificationStatus.VerifiedDate, result.VerifiedDate);
        Assert.Equal(verificationStatus.CanSendVerification, result.CanSendVerification);
        Assert.Equal(verificationStatus.TargetValue, result.TargetValue);
        Assert.Equal(VerificationType.Email, result.VerificationType);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
