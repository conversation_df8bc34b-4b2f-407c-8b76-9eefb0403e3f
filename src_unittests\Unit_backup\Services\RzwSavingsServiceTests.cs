using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.RewardTests.Unit.Services
{
    public class RzwSavingsServiceTests : IDisposable
    {
        private readonly AppDbContext _context;
        private readonly Mock<ILogger<RzwSavingsService>> _mockLogger;
        private readonly Mock<IRzwSavingsInterestService> _mockInterestService;
        private readonly Mock<IRzwWalletBalanceManagementService> _mockBalanceService;
        private readonly Mock<IRzwSavingsPlanService> _mockPlanService;
        private readonly Mock<ITokenPriceService> _mockTokenPriceService;
        private readonly Mock<IStringLocalizer<SharedResource>> _mockLocalizer;
        private readonly RzwSavingsService _service;

        public RzwSavingsServiceTests()
        {
            // Setup in-memory database
            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .ConfigureWarnings(warnings => warnings.Ignore(InMemoryEventId.TransactionIgnoredWarning))
                .Options;
            _context = new AppDbContext(options);

            // Setup mocks
            _mockLogger = new Mock<ILogger<RzwSavingsService>>();
            _mockInterestService = new Mock<IRzwSavingsInterestService>(MockBehavior.Strict);
            _mockBalanceService = new Mock<IRzwWalletBalanceManagementService>(MockBehavior.Strict);
            _mockPlanService = new Mock<IRzwSavingsPlanService>(MockBehavior.Strict);
            _mockTokenPriceService = new Mock<ITokenPriceService>(MockBehavior.Strict);
            _mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();

            // Setup default localizer responses
            _mockLocalizer.Setup(x => x[It.IsAny<string>()])
                .Returns((string key) => new LocalizedString(key, key));

            // Create service instance
            _service = new RzwSavingsService(
                _context,
                _mockBalanceService.Object,
                _mockPlanService.Object,
                _mockInterestService.Object,
                _mockTokenPriceService.Object,
                _mockLocalizer.Object,
                _mockLogger.Object
            );
        }

        public void Dispose()
        {
            _context.Dispose();
        }

        #region Create Savings Account Tests

        [Fact]
        public async Task CreateSavingsAccountAsync_WithValidData_ShouldCreateAccountAndLockBalance()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };
            var plan = new RzwSavingsPlan
            {
                Id = 1,
                Name = "Monthly Plan",
                TermType = "Monthly",
                TermDuration = 1,
                InterestRate = 0.01m,
                MinRzwAmount = 1000m,
                MaxRzwAmount = 100000m,
                IsActive = true
            };

            await _context.Users.AddAsync(user);
            await _context.RzwSavingsPlans.AddAsync(plan);
            await _context.SaveChangesAsync();

            // Setup mocks
            _mockPlanService.Setup(x => x.GetPlanByIdAsync(plan.Id))
                .ReturnsAsync(plan);
            _mockPlanService.Setup(x => x.ValidatePlanAsync(plan.Id, 5000m))
                .ReturnsAsync(true);
            _mockBalanceService.Setup(x => x.HasSufficientAvailableRzwAsync(1, 5000m))
                .ReturnsAsync(true);
            _mockBalanceService.Setup(x => x.LockRzwForSavingsAsync(user.UserId, 5000m, It.IsAny<string>(), It.IsAny<AppDbContext>(), It.IsAny<int>()))
                .ReturnsAsync(true);

            // Act
            var result = await _service.CreateSavingsAccountAsync(user.UserId, plan.Id, 5000m, false);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Account);

            var account = await _context.RzwSavingsAccounts
                .FirstOrDefaultAsync(x => x.UserId == user.UserId);
            Assert.NotNull(account);
            Assert.Equal(5000m, account.RzwAmount);
            Assert.Equal(plan.Id, account.PlanId);
            Assert.Equal(RzwSavingsStatus.Active, account.Status);

            // Verify balance was locked
            _mockBalanceService.Verify(x => x.LockRzwForSavingsAsync(user.UserId, 5000m, It.IsAny<string>(), It.IsAny<AppDbContext>(), It.IsAny<int>()), Times.Once);
        }

        [Fact]
        public async Task CreateSavingsAccountAsync_WithInsufficientBalance_ShouldReturnFailure()
        {

            var plan = new RzwSavingsPlan
            {
                Id = 1,
                Name = "Monthly Plan",
                TermType = "Monthly",
                InterestRate = 0.01m,
                MinRzwAmount = 1000m,
                MaxRzwAmount = 100000m,
                IsActive = true
            };

            await _context.RzwSavingsPlans.AddAsync(plan);
            await _context.SaveChangesAsync();

            // Setup mocks - insufficient balance
            _mockPlanService.Setup(x => x.GetPlanByIdAsync(1))
                .ReturnsAsync(plan);
            _mockPlanService.Setup(x => x.ValidatePlanAsync(1, 5000m))
                .ReturnsAsync(true);
            _mockBalanceService.Setup(x => x.HasSufficientAvailableRzwAsync(1, 5000m))
                .ReturnsAsync(false);

            // Act
            var result = await _service.CreateSavingsAccountAsync(1, 1, 5000m, false);

            // Assert
            Assert.False(result.Success);

            var account = await _context.RzwSavingsAccounts
                .FirstOrDefaultAsync(x => x.UserId == 1);
            Assert.Null(account);
        }

        [Fact]
        public async Task CreateSavingsAccountAsync_WithInvalidPlan_ShouldReturnFailure()
        {
            // Setup mocks - plan not found
            _mockPlanService.Setup(x => x.GetPlanByIdAsync(999))
                .ReturnsAsync((RzwSavingsPlan?)null);

            // Act
            var result = await _service.CreateSavingsAccountAsync(1, 999, 5000m, false);

            // Assert
            Assert.False(result.Success);
        }

        #endregion

        #region Early Withdrawal Tests

        [Fact]
        public async Task EarlyWithdrawAsync_WithValidAccount_ShouldCalculateInterestAndUnlockBalance()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };
            var plan = new RzwSavingsPlan
            {
                Id = 1,
                Name = "Monthly Plan",
                TermType = "Monthly",
                InterestRate = 0.01m,
                MinRzwAmount = 1000m,
                MaxRzwAmount = 100000m,
                IsActive = true
            };
            var account = new RzwSavingsAccount
            {
                Id = 1,
                UserId = user.UserId,
                PlanId = plan.Id,
                RzwAmount = 5000m,
                Status = RzwSavingsStatus.Active,
                StartDate = DateTime.UtcNow.AddDays(-15), // 15 days old
                MaturityDate = DateTime.UtcNow.AddDays(15), // 30 days total
                TermType = "Monthly",
                TermDuration = 30,
                InterestRate = 0.01m,
                EarlyWithdrawalPenalty = 0.1m
            };

            await _context.Users.AddAsync(user);
            await _context.RzwSavingsPlans.AddAsync(plan);
            await _context.RzwSavingsAccounts.AddAsync(account);
            await _context.SaveChangesAsync();

            // Setup mocks
            _mockInterestService.Setup(x => x.CalculateEarlyWithdrawalInterestAsync(It.IsAny<RzwSavingsAccount>()))
                .ReturnsAsync(25m); // Expected interest for 15 days
            _mockBalanceService.Setup(x => x.UnlockRzwFromSavingsAsync(user.UserId, 5000m, It.IsAny<string>(), It.IsAny<TradeType>(), It.IsAny<AppDbContext>(), It.IsAny<int>()))
                .ReturnsAsync(true);
            _mockBalanceService.Setup(x => x.AddRzwInterestAsync(account.Id, user.UserId, It.IsAny<decimal>(), It.IsAny<string>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync(new Wallet { Id = 1, UserId = user.UserId, CoinId = 1, Balance = 1000m });

            // Act
            var result = await _service.EarlyWithdrawAsync(1, 1);

            // Assert
            Assert.True(result.Success);

            var updatedAccount = await _context.RzwSavingsAccounts.FindAsync(1);
            Assert.NotNull(updatedAccount);
            Assert.Equal(RzwSavingsStatus.Withdrawn, updatedAccount.Status);

            // Verify services were called
            _mockInterestService.Verify(x => x.CalculateEarlyWithdrawalInterestAsync(It.IsAny<RzwSavingsAccount>()), Times.Once);
            _mockBalanceService.Verify(x => x.UnlockRzwFromSavingsAsync(1, 5000m, It.IsAny<string>(), It.IsAny<TradeType>(), It.IsAny<AppDbContext>(), It.IsAny<int>()), Times.Once);
        }

        [Fact]
        public async Task EarlyWithdrawAsync_WithNonExistentAccount_ShouldReturnFailure()
        {
            // Act
            var result = await _service.EarlyWithdrawAsync(999, 1);

            // Assert
            Assert.False(result.Success);
            Assert.Contains("not found", result.Message.ToLower());
        }

        [Fact]
        public async Task EarlyWithdrawAsync_WithAlreadyWithdrawnAccount_ShouldReturnFailure()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };

            var account = new RzwSavingsAccount
            {
                UserId = 1,
                PlanId = 1,
                RzwAmount = 5000m,
                Status = RzwSavingsStatus.Withdrawn, // Already withdrawn
                StartDate = DateTime.UtcNow.AddDays(-15),
                MaturityDate = DateTime.UtcNow.AddDays(15),
                TermType = "Monthly",
                TermDuration = 30,
                InterestRate = 0.01m
            };

            await _context.Users.AddAsync(user);
            await _context.RzwSavingsAccounts.AddAsync(account);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.EarlyWithdrawAsync(account.Id, 1);

            // Assert
            Assert.False(result.Success);
            // The account exists but has Withdrawn status, so it should return "not active" message
            // However, if GetSavingsAccountAsync doesn't find it due to status filtering, it returns "not found"
            // Both are valid failure scenarios, so we check for either
            Assert.True(result.Message.ToLower().Contains("not active") || result.Message.ToLower().Contains("not found"));
        }

        #endregion

        #region Maturity Processing Tests

        [Fact]
        public async Task ProcessMaturityAsync_WithMatureAccount_ShouldUnlockBalance()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };
            var plan = new RzwSavingsPlan
            {
                Id = 1,
                Name = "Monthly Plan",
                TermType = "Monthly",
                InterestRate = 0.01m,
                MinRzwAmount = 1000m,
                MaxRzwAmount = 100000m,
                IsActive = true
            };
            var account = new RzwSavingsAccount
            {
                Id = 1,
                UserId = 1,
                PlanId = 1,
                RzwAmount = 5000m,
                Status = RzwSavingsStatus.Active,
                StartDate = DateTime.UtcNow.AddDays(-30), // 30 days old
                MaturityDate = DateTime.UtcNow.AddDays(-1), // Matured yesterday
                TermType = "Monthly",
                TermDuration = 30,
                InterestRate = 0.01m
            };

            await _context.Users.AddAsync(user);
            await _context.RzwSavingsPlans.AddAsync(plan);
            await _context.RzwSavingsAccounts.AddAsync(account);
            await _context.SaveChangesAsync();

            // Setup mocks
            _mockBalanceService.Setup(x => x.UnlockRzwFromSavingsAsync(1, 5000m, It.IsAny<string>(), It.IsAny<TradeType>(), It.IsAny<AppDbContext>(), It.IsAny<int>()))
                .ReturnsAsync(true);

            // Act
            var result = await _service.ProcessMaturityAsync(1);

            // Assert
            Assert.True(result.Success);

            var updatedAccount = await _context.RzwSavingsAccounts.FindAsync(1);
            Assert.NotNull(updatedAccount);
            Assert.Equal(RzwSavingsStatus.Matured, updatedAccount.Status);

            // Verify services were called
            _mockBalanceService.Verify(x => x.UnlockRzwFromSavingsAsync(1, 5000m, It.IsAny<string>(), It.IsAny<TradeType>(), It.IsAny<AppDbContext>(), It.IsAny<int>()), Times.Once);
        }

        [Fact]
        public async Task ProcessMaturityAsync_WithNotYetMatureAccount_ShouldReturnFailure()
        {
            // Arrange
            var account = new RzwSavingsAccount
            {
                Id = 1,
                UserId = 1,
                PlanId = 1,
                RzwAmount = 5000m,
                Status = RzwSavingsStatus.Active,
                StartDate = DateTime.UtcNow.AddDays(-15), // 15 days old
                MaturityDate = DateTime.UtcNow.AddDays(15), // Still 15 days to go
                TermType = "Monthly",
                TermDuration = 30,
                InterestRate = 0.01m
            };

            await _context.RzwSavingsAccounts.AddAsync(account);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.ProcessMaturityAsync(1);

            // Assert
            Assert.False(result.Success);
            Assert.Contains("not", result.Message.ToLower());
        }

        #endregion
    }
}
