using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.MyAccount.Pages;
using RazeWinComTr.Areas.MyAccount.ViewModels;
using System.Security.Claims;
using Xunit;

namespace RazeWinComTr.Tests.Unit.Pages;

public class ProfilePageTests : IDisposable
{
    private readonly AppDbContext _context;
    private readonly Mock<IEmailVerificationService> _mockEmailVerificationService;
    private readonly Mock<IStringLocalizer<SharedResource>> _mockLocalizer;
    private readonly Mock<ILogger<ProfileModel>> _mockLogger;
    private readonly ProfileModel _profileModel;

    public ProfilePageTests()
    {
        var options = new DbContextOptionsBuilder<AppDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AppDbContext(options);
        _mockEmailVerificationService = new Mock<IEmailVerificationService>();
        _mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();
        _mockLogger = new Mock<ILogger<ProfileModel>>();

        _profileModel = new ProfileModel(
            _context,
            _mockEmailVerificationService.Object,
            _mockLocalizer.Object,
            _mockLogger.Object);

        SetupMockLocalizer();
        SeedTestData();
    }

    private void SetupMockLocalizer()
    {
        _mockLocalizer.Setup(l => l["Verification email sent successfully"])
            .Returns(new LocalizedString("Verification email sent successfully", "Verification email sent successfully"));
        _mockLocalizer.Setup(l => l["Failed to send verification email. Please try again later."])
            .Returns(new LocalizedString("Failed to send verification email. Please try again later.", "Failed to send verification email. Please try again later."));
        _mockLocalizer.Setup(l => l["You can send another verification email in {0} minutes."])
            .Returns(new LocalizedString("You can send another verification email in {0} minutes.", "You can send another verification email in {0} minutes."));
        _mockLocalizer.Setup(l => l["You have reached the daily limit. Try again tomorrow."])
            .Returns(new LocalizedString("You have reached the daily limit. Try again tomorrow.", "You have reached the daily limit. Try again tomorrow."));
    }

    private void SeedTestData()
    {
        var user = new User
        {
            UserId = 1,
            Email = "<EMAIL>",
            Name = "Profile",
            Surname = "Test",
            IdentityNumber = "12345678901",
            PhoneNumber = "+905551234567",
            Password = "hashedpassword",
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };

        _context.Users.Add(user);
        _context.SaveChanges();
    }

    private void SetupHttpContext(int userId)
    {
        var claims = new List<Claim>
        {
            new Claim("UserId", userId.ToString()),
            new Claim(ClaimTypes.Email, "<EMAIL>")
        };

        var identity = new ClaimsIdentity(claims, "TestAuthType");
        var principal = new ClaimsPrincipal(identity);

        var httpContext = new DefaultHttpContext();
        httpContext.User = principal;

        _profileModel.PageContext = new PageContext
        {
            HttpContext = httpContext
        };
    }

    [Fact]
    public async Task OnGetAsync_WithVerifiedEmail_ShouldShowVerifiedStatus()
    {
        // Arrange
        int userId = 1;
        SetupHttpContext(userId);

        var emailStatus = new EmailVerificationStatus
        {
            IsVerified = true,
            VerifiedDate = DateTime.UtcNow.AddDays(-1),
            CanSendVerification = false,
            TimeUntilNextVerification = null,
            TodayAttempts = 0,
            MaxDailyAttempts = 5,
            MinutesBetweenAttempts = 5,
            TargetValue = "<EMAIL>"
        };

        _mockEmailVerificationService.Setup(e => e.GetVerificationStatusAsync(userId))
            .ReturnsAsync(emailStatus);

        // Act
        var result = await _profileModel.OnGetAsync();

        // Assert
        Assert.IsType<PageResult>(result);
        Assert.True(_profileModel.EmailVerification.IsVerified);
        Assert.NotNull(_profileModel.EmailVerification.VerifiedDate);
        Assert.False(_profileModel.EmailVerification.CanSendVerification);
        Assert.Equal("<EMAIL>", _profileModel.EmailVerification.EmailAddress);
    }

    [Fact]
    public async Task OnGetAsync_WithUnverifiedEmail_ShouldShowUnverifiedStatus()
    {
        // Arrange
        int userId = 1;
        SetupHttpContext(userId);

        var emailStatus = new EmailVerificationStatus
        {
            IsVerified = false,
            VerifiedDate = null,
            CanSendVerification = true,
            TimeUntilNextVerification = null,
            TodayAttempts = 0,
            MaxDailyAttempts = 5,
            MinutesBetweenAttempts = 5,
            TargetValue = "<EMAIL>"
        };

        _mockEmailVerificationService.Setup(e => e.GetVerificationStatusAsync(userId))
            .ReturnsAsync(emailStatus);

        // Act
        var result = await _profileModel.OnGetAsync();

        // Assert
        Assert.IsType<PageResult>(result);
        Assert.False(_profileModel.EmailVerification.IsVerified);
        Assert.Null(_profileModel.EmailVerification.VerifiedDate);
        Assert.True(_profileModel.EmailVerification.CanSendVerification);
        Assert.Equal("<EMAIL>", _profileModel.EmailVerification.EmailAddress);
    }

    [Fact]
    public async Task OnGetAsync_WithRateLimitedEmail_ShouldShowRateLimitStatus()
    {
        // Arrange
        int userId = 1;
        SetupHttpContext(userId);

        var emailStatus = new EmailVerificationStatus
        {
            IsVerified = false,
            VerifiedDate = null,
            CanSendVerification = false,
            TimeUntilNextVerification = TimeSpan.FromMinutes(3),
            TodayAttempts = 2,
            MaxDailyAttempts = 5,
            MinutesBetweenAttempts = 5,
            TargetValue = "<EMAIL>"
        };

        _mockEmailVerificationService.Setup(e => e.GetVerificationStatusAsync(userId))
            .ReturnsAsync(emailStatus);

        // Act
        var result = await _profileModel.OnGetAsync();

        // Assert
        Assert.IsType<PageResult>(result);
        Assert.False(_profileModel.EmailVerification.IsVerified);
        Assert.False(_profileModel.EmailVerification.CanSendVerification);
        Assert.NotNull(_profileModel.EmailVerification.TimeUntilNext);
        Assert.Equal(3, Math.Ceiling(_profileModel.EmailVerification.TimeUntilNext.Value.TotalMinutes));
        Assert.Equal(2, _profileModel.EmailVerification.TodayAttempts);
    }

    [Fact]
    public async Task OnPostSendVerificationEmailAsync_WithValidUser_ShouldSendEmail()
    {
        // Arrange
        int userId = 1;
        SetupHttpContext(userId);

        _mockEmailVerificationService.Setup(e => e.SendVerificationEmailAsync(
            userId, "<EMAIL>", "Profile Test"))
            .ReturnsAsync(true);

        // Act
        var result = await _profileModel.OnPostSendVerificationEmailAsync();

        // Assert
        var redirectResult = Assert.IsType<RedirectToPageResult>(result);
        Assert.Equal("/Profile", redirectResult.PageName);
        
        _mockEmailVerificationService.Verify(e => e.SendVerificationEmailAsync(
            userId, "<EMAIL>", "Profile Test"), Times.Once);
    }

    [Fact]
    public async Task OnPostSendVerificationEmailAsync_WithFailedEmail_ShouldShowError()
    {
        // Arrange
        int userId = 1;
        SetupHttpContext(userId);

        _mockEmailVerificationService.Setup(e => e.SendVerificationEmailAsync(
            userId, "<EMAIL>", "Profile Test"))
            .ReturnsAsync(false);

        // Act
        var result = await _profileModel.OnPostSendVerificationEmailAsync();

        // Assert
        var redirectResult = Assert.IsType<RedirectToPageResult>(result);
        Assert.Equal("/Profile", redirectResult.PageName);
        
        // Should have set error message in TempData
        // Note: In a real test, you'd need to mock TempData as well
    }

    [Fact]
    public async Task OnPostSendVerificationEmailAsync_WithoutAuthentication_ShouldRedirectToLogin()
    {
        // Arrange - No authentication setup

        // Act
        var result = await _profileModel.OnPostSendVerificationEmailAsync();

        // Assert
        var redirectResult = Assert.IsType<RedirectToPageResult>(result);
        Assert.Equal("/Login", redirectResult.PageName);
    }

    [Fact]
    public async Task OnPostSendVerificationEmailAsync_WithNonExistentUser_ShouldShowError()
    {
        // Arrange
        int nonExistentUserId = 999;
        
        var claims = new List<Claim>
        {
            new Claim("UserId", nonExistentUserId.ToString()),
            new Claim(ClaimTypes.Email, "<EMAIL>")
        };

        var identity = new ClaimsIdentity(claims, "TestAuthType");
        var principal = new ClaimsPrincipal(identity);

        var httpContext = new DefaultHttpContext();
        httpContext.User = principal;

        _profileModel.PageContext = new PageContext
        {
            HttpContext = httpContext
        };

        // Act
        var result = await _profileModel.OnPostSendVerificationEmailAsync();

        // Assert
        var redirectResult = Assert.IsType<RedirectToPageResult>(result);
        Assert.Equal("/Profile", redirectResult.PageName);
        
        // Should not have called email service
        _mockEmailVerificationService.Verify(e => e.SendVerificationEmailAsync(
            It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }

    [Theory]
    [InlineData(true, "fas fa-check-circle")]
    [InlineData(false, "fas fa-exclamation-triangle")]
    public void EmailVerificationViewModel_GetStatusIconClass_ShouldReturnCorrectIcon(bool isVerified, string expectedIcon)
    {
        // Arrange
        var viewModel = new EmailVerificationViewModel
        {
            IsVerified = isVerified
        };

        // Act
        var iconClass = viewModel.GetStatusIconClass();

        // Assert
        Assert.Equal(expectedIcon, iconClass);
    }

    [Theory]
    [InlineData(true, "text-success")]
    [InlineData(false, "text-warning")]
    public void EmailVerificationViewModel_GetStatusCssClass_ShouldReturnCorrectClass(bool isVerified, string expectedClass)
    {
        // Arrange
        var viewModel = new EmailVerificationViewModel
        {
            IsVerified = isVerified
        };

        // Act
        var cssClass = viewModel.GetStatusCssClass();

        // Assert
        Assert.Equal(expectedClass, cssClass);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
